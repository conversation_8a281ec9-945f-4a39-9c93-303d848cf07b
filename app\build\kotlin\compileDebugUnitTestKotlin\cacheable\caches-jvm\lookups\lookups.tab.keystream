  
Completion com.example.habits9.data  	DayOfWeek com.example.habits9.data  EnhancedFrequency com.example.habits9.data  FirestoreConverters com.example.habits9.data  
FrequencyType com.example.habits9.data  Habit com.example.habits9.data  HabitRepositoryTest com.example.habits9.data  HabitSection com.example.habits9.data  	HabitType com.example.habits9.data  NumericalHabitType com.example.habits9.data  String com.example.habits9.data  Test com.example.habits9.data  assertEquals com.example.habits9.data  assertFalse com.example.habits9.data  
assertNull com.example.habits9.data  
assertTrue com.example.habits9.data  contains com.example.habits9.data  habitToFirestore com.example.habits9.data  mapOf com.example.habits9.data  to com.example.habits9.data  habitId #com.example.habits9.data.Completion  id #com.example.habits9.data.Completion  	timestamp #com.example.habits9.data.Completion  value #com.example.habits9.data.Completion  	Companion "com.example.habits9.data.DayOfWeek  MONDAY "com.example.habits9.data.DayOfWeek  SUNDAY "com.example.habits9.data.DayOfWeek  THURSDAY "com.example.habits9.data.DayOfWeek  TUESDAY "com.example.habits9.data.DayOfWeek  	WEDNESDAY "com.example.habits9.data.DayOfWeek  	Companion &com.example.habits9.data.FrequencyType  DAILY &com.example.habits9.data.FrequencyType  MONTHLY &com.example.habits9.data.FrequencyType  WEEKLY &com.example.habits9.data.FrequencyType  color com.example.habits9.data.Habit  completionDatesJson com.example.habits9.data.Habit  creationDate com.example.habits9.data.Habit  
currentStreak com.example.habits9.data.Habit  
dayOfMonth com.example.habits9.data.Habit  dayOfWeekInMonth com.example.habits9.data.Habit  
daysOfWeek com.example.habits9.data.Habit  description com.example.habits9.data.Habit  
frequencyType com.example.habits9.data.Habit  id com.example.habits9.data.Habit  
isArchived com.example.habits9.data.Habit  name com.example.habits9.data.Habit  position com.example.habits9.data.Habit  repeatsEvery com.example.habits9.data.Habit  
targetType com.example.habits9.data.Habit  targetValue com.example.habits9.data.Habit  type com.example.habits9.data.Habit  unit com.example.habits9.data.Habit  uuid com.example.habits9.data.Habit  weekOfMonth com.example.habits9.data.Habit  FirestoreConverters ,com.example.habits9.data.HabitRepositoryTest  Habit ,com.example.habits9.data.HabitRepositoryTest  assertEquals ,com.example.habits9.data.HabitRepositoryTest  assertFalse ,com.example.habits9.data.HabitRepositoryTest  
assertNull ,com.example.habits9.data.HabitRepositoryTest  
assertTrue ,com.example.habits9.data.HabitRepositoryTest  contains ,com.example.habits9.data.HabitRepositoryTest  habitToFirestore ,com.example.habits9.data.HabitRepositoryTest  mapOf ,com.example.habits9.data.HabitRepositoryTest  to ,com.example.habits9.data.HabitRepositoryTest  color %com.example.habits9.data.HabitSection  displayOrder %com.example.habits9.data.HabitSection  id %com.example.habits9.data.HabitSection  name %com.example.habits9.data.HabitSection  	Companion "com.example.habits9.data.HabitType  	NUMERICAL "com.example.habits9.data.HabitType  YES_NO "com.example.habits9.data.HabitType  value "com.example.habits9.data.HabitType  AT_LEAST +com.example.habits9.data.NumericalHabitType  AT_MOST +com.example.habits9.data.NumericalHabitType  	Companion +com.example.habits9.data.NumericalHabitType  value +com.example.habits9.data.NumericalHabitType  
Completion "com.example.habits9.data.firestore  FirestoreCompletion "com.example.habits9.data.firestore  FirestoreConverters "com.example.habits9.data.firestore  FirestoreConvertersTest "com.example.habits9.data.firestore  FirestoreHabit "com.example.habits9.data.firestore  FirestoreHabitSection "com.example.habits9.data.firestore  Habit "com.example.habits9.data.firestore  HabitSection "com.example.habits9.data.firestore  	HabitType "com.example.habits9.data.firestore  NumericalHabitType "com.example.habits9.data.firestore  Test "com.example.habits9.data.firestore  assertEquals "com.example.habits9.data.firestore  
assertNull "com.example.habits9.data.firestore  completionToFirestore "com.example.habits9.data.firestore  firestoreToCompletion "com.example.habits9.data.firestore  firestoreToHabit "com.example.habits9.data.firestore  firestoreToHabitSection "com.example.habits9.data.firestore  habitSectionToFirestore "com.example.habits9.data.firestore  habitToFirestore "com.example.habits9.data.firestore  habitId 6com.example.habits9.data.firestore.FirestoreCompletion  id 6com.example.habits9.data.firestore.FirestoreCompletion  	timestamp 6com.example.habits9.data.firestore.FirestoreCompletion  value 6com.example.habits9.data.firestore.FirestoreCompletion  completionToFirestore 6com.example.habits9.data.firestore.FirestoreConverters  firestoreToCompletion 6com.example.habits9.data.firestore.FirestoreConverters  firestoreToHabit 6com.example.habits9.data.firestore.FirestoreConverters  firestoreToHabitSection 6com.example.habits9.data.firestore.FirestoreConverters  habitSectionToFirestore 6com.example.habits9.data.firestore.FirestoreConverters  habitToFirestore 6com.example.habits9.data.firestore.FirestoreConverters  
Completion :com.example.habits9.data.firestore.FirestoreConvertersTest  FirestoreCompletion :com.example.habits9.data.firestore.FirestoreConvertersTest  FirestoreConverters :com.example.habits9.data.firestore.FirestoreConvertersTest  FirestoreHabit :com.example.habits9.data.firestore.FirestoreConvertersTest  FirestoreHabitSection :com.example.habits9.data.firestore.FirestoreConvertersTest  Habit :com.example.habits9.data.firestore.FirestoreConvertersTest  HabitSection :com.example.habits9.data.firestore.FirestoreConvertersTest  	HabitType :com.example.habits9.data.firestore.FirestoreConvertersTest  NumericalHabitType :com.example.habits9.data.firestore.FirestoreConvertersTest  assertEquals :com.example.habits9.data.firestore.FirestoreConvertersTest  
assertNull :com.example.habits9.data.firestore.FirestoreConvertersTest  completionToFirestore :com.example.habits9.data.firestore.FirestoreConvertersTest  firestoreToCompletion :com.example.habits9.data.firestore.FirestoreConvertersTest  firestoreToHabit :com.example.habits9.data.firestore.FirestoreConvertersTest  firestoreToHabitSection :com.example.habits9.data.firestore.FirestoreConvertersTest  habitSectionToFirestore :com.example.habits9.data.firestore.FirestoreConvertersTest  habitToFirestore :com.example.habits9.data.firestore.FirestoreConvertersTest  color 1com.example.habits9.data.firestore.FirestoreHabit  completionDatesJson 1com.example.habits9.data.firestore.FirestoreHabit  creationDate 1com.example.habits9.data.firestore.FirestoreHabit  
currentStreak 1com.example.habits9.data.firestore.FirestoreHabit  
dayOfMonth 1com.example.habits9.data.firestore.FirestoreHabit  dayOfWeekInMonth 1com.example.habits9.data.firestore.FirestoreHabit  
daysOfWeek 1com.example.habits9.data.firestore.FirestoreHabit  description 1com.example.habits9.data.firestore.FirestoreHabit  
frequencyType 1com.example.habits9.data.firestore.FirestoreHabit  id 1com.example.habits9.data.firestore.FirestoreHabit  
isArchived 1com.example.habits9.data.firestore.FirestoreHabit  name 1com.example.habits9.data.firestore.FirestoreHabit  position 1com.example.habits9.data.firestore.FirestoreHabit  repeatsEvery 1com.example.habits9.data.firestore.FirestoreHabit  
targetType 1com.example.habits9.data.firestore.FirestoreHabit  targetValue 1com.example.habits9.data.firestore.FirestoreHabit  type 1com.example.habits9.data.firestore.FirestoreHabit  unit 1com.example.habits9.data.firestore.FirestoreHabit  uuid 1com.example.habits9.data.firestore.FirestoreHabit  weekOfMonth 1com.example.habits9.data.firestore.FirestoreHabit  color 8com.example.habits9.data.firestore.FirestoreHabitSection  displayOrder 8com.example.habits9.data.firestore.FirestoreHabitSection  id 8com.example.habits9.data.firestore.FirestoreHabitSection  name 8com.example.habits9.data.firestore.FirestoreHabitSection  	LocalDate com.example.habits9.ui  Test com.example.habits9.ui  WeekBoundaryUtils com.example.habits9.ui  WeekBoundaryUtilsTest com.example.habits9.ui  assertEquals com.example.habits9.ui  
assertTrue com.example.habits9.ui  getCurrentWeekDates com.example.habits9.ui  
getWeekEnd com.example.habits9.ui  
getWeekNumber com.example.habits9.ui  getWeekStart com.example.habits9.ui  java com.example.habits9.ui  WeekBoundaryUtils $com.example.habits9.ui.MainViewModel  getCurrentWeekDates 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekEnd 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  
getWeekNumber 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  getWeekStart 6com.example.habits9.ui.MainViewModel.WeekBoundaryUtils  	LocalDate ,com.example.habits9.ui.WeekBoundaryUtilsTest  WeekBoundaryUtils ,com.example.habits9.ui.WeekBoundaryUtilsTest  assertEquals ,com.example.habits9.ui.WeekBoundaryUtilsTest  
assertTrue ,com.example.habits9.ui.WeekBoundaryUtilsTest  getCurrentWeekDates ,com.example.habits9.ui.WeekBoundaryUtilsTest  
getWeekEnd ,com.example.habits9.ui.WeekBoundaryUtilsTest  
getWeekNumber ,com.example.habits9.ui.WeekBoundaryUtilsTest  getWeekStart ,com.example.habits9.ui.WeekBoundaryUtilsTest  java ,com.example.habits9.ui.WeekBoundaryUtilsTest  	DayOfWeek com.example.habits9.utils  EnhancedFrequency com.example.habits9.utils  
FrequencyType com.example.habits9.utils  Habit com.example.habits9.utils  HabitScheduler com.example.habits9.utils  HabitSchedulerTest com.example.habits9.utils  	LocalDate com.example.habits9.utils  Test com.example.habits9.utils  ZoneId com.example.habits9.utils  assertEquals com.example.habits9.utils  assertFalse com.example.habits9.utils  
assertTrue com.example.habits9.utils  isHabitScheduled com.example.habits9.utils  isScheduledWithFrequency com.example.habits9.utils  listOf com.example.habits9.utils  isHabitScheduled (com.example.habits9.utils.HabitScheduler  isScheduledWithFrequency (com.example.habits9.utils.HabitScheduler  	DayOfWeek ,com.example.habits9.utils.HabitSchedulerTest  EnhancedFrequency ,com.example.habits9.utils.HabitSchedulerTest  
FrequencyType ,com.example.habits9.utils.HabitSchedulerTest  Habit ,com.example.habits9.utils.HabitSchedulerTest  HabitScheduler ,com.example.habits9.utils.HabitSchedulerTest  	LocalDate ,com.example.habits9.utils.HabitSchedulerTest  ZoneId ,com.example.habits9.utils.HabitSchedulerTest  assertEquals ,com.example.habits9.utils.HabitSchedulerTest  assertFalse ,com.example.habits9.utils.HabitSchedulerTest  
assertTrue ,com.example.habits9.utils.HabitSchedulerTest  creationTimestamp ,com.example.habits9.utils.HabitSchedulerTest  isHabitScheduled ,com.example.habits9.utils.HabitSchedulerTest  isScheduledWithFrequency ,com.example.habits9.utils.HabitSchedulerTest  listOf ,com.example.habits9.utils.HabitSchedulerTest  
referenceDate ,com.example.habits9.utils.HabitSchedulerTest  ExampleUnitTest com.example.uhabits_99  Test com.example.uhabits_99  assertEquals com.example.uhabits_99  assertEquals &com.example.uhabits_99.ExampleUnitTest  Class 	java.lang  	DayOfWeek 	java.time  	LocalDate 	java.time  ZoneId 	java.time  toEpochMilli java.time.Instant  atStartOfDay java.time.LocalDate  atTime java.time.LocalDate  	compareTo java.time.LocalDate  	minusDays java.time.LocalDate  now java.time.LocalDate  of java.time.LocalDate  
ofEpochDay java.time.LocalDate  plusDays java.time.LocalDate  
toEpochDay java.time.LocalDate  atZone java.time.LocalDateTime  
systemDefault java.time.ZoneId  	toInstant java.time.ZonedDateTime  
ChronoUnit java.time.temporal  DAYS java.time.temporal.ChronoUnit  between java.time.temporal.ChronoUnit  Pair kotlin  to kotlin  plus 
kotlin.Int  times 
kotlin.Int  div kotlin.Long  times kotlin.Long  toInt kotlin.Long  toString kotlin.Long  contains 
kotlin.String  to 
kotlin.String  List kotlin.collections  Map kotlin.collections  contains kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  get kotlin.collections.List  size kotlin.collections.List  get kotlin.collections.Map  java 
kotlin.jvm  contains 
kotlin.ranges  contains kotlin.sequences  contains kotlin.text  
Completion 	org.junit  FirestoreCompletion 	org.junit  FirestoreConverters 	org.junit  FirestoreHabit 	org.junit  FirestoreHabitSection 	org.junit  Habit 	org.junit  HabitSection 	org.junit  	HabitType 	org.junit  	LocalDate 	org.junit  NumericalHabitType 	org.junit  String 	org.junit  Test 	org.junit  WeekBoundaryUtils 	org.junit  assertEquals 	org.junit  assertFalse 	org.junit  
assertNull 	org.junit  
assertTrue 	org.junit  completionToFirestore 	org.junit  contains 	org.junit  firestoreToCompletion 	org.junit  firestoreToHabit 	org.junit  firestoreToHabitSection 	org.junit  getCurrentWeekDates 	org.junit  
getWeekEnd 	org.junit  
getWeekNumber 	org.junit  getWeekStart 	org.junit  habitSectionToFirestore 	org.junit  habitToFirestore 	org.junit  java 	org.junit  mapOf 	org.junit  to 	org.junit  assertEquals org.junit.Assert  assertFalse org.junit.Assert  
assertNull org.junit.Assert  
assertTrue org.junit.Assert  !HabitSerializationIntegrationTest com.example.habits9.data  listOf com.example.habits9.data  map com.example.habits9.data  split com.example.habits9.data  toInt com.example.habits9.data  Habit :com.example.habits9.data.HabitSerializationIntegrationTest  assertEquals :com.example.habits9.data.HabitSerializationIntegrationTest  assertFalse :com.example.habits9.data.HabitSerializationIntegrationTest  
assertNull :com.example.habits9.data.HabitSerializationIntegrationTest  
assertTrue :com.example.habits9.data.HabitSerializationIntegrationTest  contains :com.example.habits9.data.HabitSerializationIntegrationTest  listOf :com.example.habits9.data.HabitSerializationIntegrationTest  map :com.example.habits9.data.HabitSerializationIntegrationTest  mapOf :com.example.habits9.data.HabitSerializationIntegrationTest  split :com.example.habits9.data.HabitSerializationIntegrationTest  to :com.example.habits9.data.HabitSerializationIntegrationTest  toInt :com.example.habits9.data.HabitSerializationIntegrationTest  	Function1 kotlin  Result kotlin  map kotlin  split 
kotlin.String  toInt 
kotlin.String  map kotlin.collections  map kotlin.collections.List  Sequence kotlin.sequences  map kotlin.sequences  map kotlin.text  split kotlin.text  toInt kotlin.text  listOf 	org.junit  map 	org.junit  split 	org.junit  toInt 	org.junit                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      
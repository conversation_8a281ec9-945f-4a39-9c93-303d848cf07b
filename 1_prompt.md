# 🐞 Prompt: Fix Firestore Deserialization Crash on Reading Habits

## 1. Objective

To fix a critical app crash (`FirebaseFirestoreException: Could not deserialize object`) that occurs when the app tries to read a habit with a custom weekly frequency from Firestore.

## 2. Visual Reference & Root Cause Analysis

The exact error is captured in the stack trace provided in **`image_13f434.png`**.

This is a **deserialization error**, which is different from the previous bug.
* The previous bug was when **writing** data.
* This new bug happens when **reading** data back from Firestore.

The error message is very specific: `Failed to convert value of type java.util.ArrayList to String (found in field 'daysOfWeek')`.

**Root Cause:**
1. The `daysOfWeek` field is correctly being saved as an **Array** in Firestore.
2. However, the `Habit` data class in the Kotlin code still defines the `daysOfWeek` property as a `String`.
3. The app is crashing because it is trying to read an Array from the database and force it into a String variable, which is not possible.

## 3. Detailed Implementation Plan

The fix requires updating the local data model to correctly match the data structure in Firestore.

* **Task 3.1: Update the Habit Data Class**
    * Navigate to the `Habit.kt` file.
    * Locate the `daysOfWeek` property inside the data class.
    * Change its data type from `String` to `List<Long>`.
* **Task 3.2: Ensure Deserialization Compatibility**
    * For Firestore's automatic data conversion to work, the `Habit` data class must be easily constructible.
    * **Crucial:** Ensure every property in the `Habit` data class constructor has a default value. For the updated field, this would look like: `val daysOfWeek: List<Long> = emptyList()`.

## 4. Verification / Testing Section

* **Test Case 1: Fix the Crash on App Start**
    * Before making the changes, ensure you have a habit saved in Firestore that was causing the crash (the one created in the previous session).
    * After applying the fix, launch the app.
    * **Expected Outcome:** The app must **not** crash on startup. It should successfully read the existing habit from Firestore and display it correctly on the home screen.

* **Test Case 2: Verify the End-to-End Flow**
    * Create a new habit with a weekly frequency.
    * Save it, then close and restart the app.
    * **Expected Outcome:** The entire create-and-read process should work flawlessly without any crashes.

## 5. Mandatory Development Guidelines

**These practices must be followed during all phases of development—planning, implementation, and review.**

### 1. Refer to the Style Guide
Before starting any feature:
- Always consult the **style guide** for rules related to UI/UX, layout, naming conventions, spacing, colors, and design patterns.
- The style guide is located in the **`style.md` file in the root folder`**.
- It is the **single source of truth** for styling decisions.

### 2. Study the Reference Project
Prior to implementation:
- Review the **reference project** located in the `uhabits-dev` folder (in the root directory).
- Understand how similar features have been approached to maintain consistency and avoid duplications or contradictions.
- The reference project serves as a **blueprint** for implementation.
- This step is mandatory. **Do not proceed to implementation without this step.**

### 3. Understand the Existing Project Structure
Before writing any code:
- Spend time exploring and understanding how the current system is structured.
- Even for new features, existing components or utility functions may be reusable.
- Integrate changes **cleanly into the existing architecture** instead of creating disconnected code.

### 4. Maintain a Clean Codebase
After implementing features:
- Remove any temporary, test, or duplicate files, folders, routes, or unused components that were created during development.
- Keep the codebase **organized and clutter-free**.

### 5. Pause If There Is Any Confusion
If at any point the requirements are unclear:
- **Do not proceed** based on assumptions.
- Immediately pause and seek clarification either from the project lead or directly from me.
- It is better to get clarity than to redo or fix avoidable mistakes later.

### 6. Remove Unused Old Implementations
As part of final review:
- Identify and delete **any old, unused code** that was implemented earlier but is no longer in use.
- This includes obsolete routes, modules, features, or legacy logic.

# Please follow the below debugging guidelines while resolving the issue 

# Debug Process Guide

Please follow this exact structured debugging process to investigate and resolve the issue:


## 1. Understand the Error

- thoroughly understand the error context!
- Carefully interpret what the error says:
  - Understand the type of exception
  - Analyze the stack trace
  - Note any file paths or line numbers referenced

## 2. Trace the Error Location

- Navigate to all relevant files and lines mentioned in the error log
- Understand the surrounding code and the full implementation of the feature where the error occurred
- Focus on:
  - Control flow
  - Input/output
  - Any dependent components

## 3. Comprehend the Current Feature Implementation

- Develop a clear mental model of how the entire feature is structured and functions
- Identify how the involved files and methods interact
- Understand what they're intended to do

## 4. Determine the Root Cause

> **Important**: Before implementing any changes, it is mandatory to identify and finalize the true root cause of the issue.

Think deeply about potential causes:
- Logic error
- Missing configuration
- Incompatible encoding
- Race condition
- Misused library

**Clearly state the root cause once identified.**

## 5. Cross-Reference the Reference Project

- Once the root cause is finalized, examine the reference project at `./uhabits-dev`
- Compare relevant parts of the implementation
- Look for differences or proven approaches that could guide the solution

## 6. Plan and Execute the Fix

After gaining full understanding and validating it against the reference project, proceed to implement the fix with precision.

Ensure that:
1. The change is minimal and localized
2. It addresses the root cause directly
3. It does not introduce side effects

## 7. Verify

Test the fix thoroughly to ensure the issue is resolved.
